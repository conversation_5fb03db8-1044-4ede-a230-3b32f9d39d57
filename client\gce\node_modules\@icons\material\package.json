{"version": "0.2.4", "name": "@icons/material", "main": "index.js", "scripts": {"transform": "node transform.js", "build": "babel icons -d .", "prepublish": "npm run build"}, "author": "case <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/at-icons/material.git"}, "devDependencies": {"@case/eslint-config": "^0.3.3", "babel-cli": "^6.24.0", "babel-plugin-transform-class-properties": "^6.23.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.2.2", "babel-preset-flow": "^6.23.0", "babel-preset-react": "^6.23.0", "change-case": "^3.0.1", "fs-extra": "^4.0.1", "mdi-svg": "^2.2.43"}, "peerDependencies": {"react": "*"}, "eslintConfig": {"extends": "@case"}, "babel": {"plugins": ["transform-class-properties", "transform-object-rest-spread"], "presets": ["env", "flow", "react"]}}