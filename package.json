{"name": "gce", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"doc": "docs"}, "scripts": {"format": "prettier --write .", "repack": "node repack.js", "clean": "rm -rf package-lock.json node_modules ./client/gce/package-lock.json ./client/gce/node_modules ./client/offer/package-lock.json ./client/offer/node_modules ./client/config/package-lock.json ./client/config/node_modules ./client/logs/package-lock.json ./client/logs/node_modules", "install-all": "npm install && cd client/gce && npm install && cd ../offer && npm install && cd ../config && npm install && cd ../logs && npm install && gulp ", "reinstall": "npm run clean && npm run install-all", "gce": "cd ./client/gce && npm start", "offer": "cd ./client/offer && npm start", "config": "cd ./client/config && npm start", "logs": "cd ./client/logs && npm start"}, "author": "", "license": "ISC", "dependencies": {"autoprefixer": "^10.2.5", "clean-css": "^5.3.3", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-dart-sass": "^1.0.2", "gulp-jshint": "^2.1.0", "gulp-postcss": "^9.0.1", "gulp-rename": "^2.0.0", "gulp-rtlcss": "^2.0.0", "gulp-sass": "^5.1.0", "gulp-uglify": "^3.0.2", "html-minifier": "^4.0.0", "install": "^0.13.0", "npm": "^10.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-select": "^5.10.1", "reactstrap": "^9.2.0", "shuji": "github:mazam<PERSON>/shuji#add-preserve-option", "uglify-js": "^3.19.3"}, "devDependencies": {"gce": "^1.0.0", "gulp": "^4.0.2", "jshint": "^2.10.3", "node": "16.17.0", "node-sass": "^7.0.1", "postcss": "^8.2.8", "sass": "^1.51.0"}}